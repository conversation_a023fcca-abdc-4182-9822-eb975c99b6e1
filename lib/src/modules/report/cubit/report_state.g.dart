// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'report_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PerformanceReportStateImpl _$$PerformanceReportStateImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceReportStateImpl(
      currentOneYearClickCount:
          (json['currentOneYearClickCount'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(DateTime.parse(k), (e as num).toInt()),
              ) ??
              const {},
      currentOneYearConversionCount:
          (json['currentOneYearConversionCount'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(DateTime.parse(k), (e as num).toInt()),
              ) ??
              const {},
      performanceMonthlyData: (json['performanceMonthlyData'] as List<dynamic>?)
              ?.map((e) => PerformanceMonthlyReportData.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      thisMonthOccurredConversionCount:
          (json['thisMonthOccurredConversionCount'] as num?)?.toInt() ?? 0,
      lastMonthApprovedReward:
          (json['lastMonthApprovedReward'] as num?)?.toDouble() ?? 0,
      topTenCampaignsClickCount:
          (json['topTenCampaignsClickCount'] as List<dynamic>?)
                  ?.map((e) =>
                      CampaignNameAndClicks.fromJson(e as Map<String, dynamic>))
                  .toList() ??
              const [],
      paymentSummary: json['paymentSummary'] == null
          ? null
          : PaymentSummary.fromJson(
              json['paymentSummary'] as Map<String, dynamic>),
      minimumPaymentDetails: json['minimumPaymentDetails'] == null
          ? null
          : MinimumPaymentDetails.fromJson(
              json['minimumPaymentDetails'] as Map<String, dynamic>),
      currency: json['currency'] as String? ?? '',
      currencyCode: json['currencyCode'] as String? ?? '',
      errorMessage: json['errorMessage'] as String? ?? '',
      isPullToRefresh: json['isPullToRefresh'] as bool? ?? false,
      country: $enumDecodeNullable(_$CountryEnumMap, json['country']),
      selectedSiteId: (json['selectedSiteId'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$PerformanceReportStateImplToJson(
        _$PerformanceReportStateImpl instance) =>
    <String, dynamic>{
      'currentOneYearClickCount': instance.currentOneYearClickCount
          .map((k, e) => MapEntry(k.toIso8601String(), e)),
      'currentOneYearConversionCount': instance.currentOneYearConversionCount
          .map((k, e) => MapEntry(k.toIso8601String(), e)),
      'performanceMonthlyData': instance.performanceMonthlyData,
      'thisMonthOccurredConversionCount':
          instance.thisMonthOccurredConversionCount,
      'lastMonthApprovedReward': instance.lastMonthApprovedReward,
      'topTenCampaignsClickCount': instance.topTenCampaignsClickCount,
      'paymentSummary': instance.paymentSummary,
      'minimumPaymentDetails': instance.minimumPaymentDetails,
      'currency': instance.currency,
      'currencyCode': instance.currencyCode,
      'errorMessage': instance.errorMessage,
      'isPullToRefresh': instance.isPullToRefresh,
      'country': _$CountryEnumMap[instance.country],
      'selectedSiteId': instance.selectedSiteId,
    };

const _$CountryEnumMap = {
  Country.INDONESIA: 'INDONESIA',
  Country.MALAYSIA: 'MALAYSIA',
  Country.SINGAPORE: 'SINGAPORE',
  Country.THAILAND: 'THAILAND',
  Country.GLOBAL: 'GLOBAL',
};
